<?php
require_once '../../config/config.php';

/** @var PDO $conn */

// Handle user deletion
if (isset($_POST['delete_user'])) {
    $user_id = $_POST['user_id'];
    $query = "DELETE FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([$user_id]);
}

// Handle user addition/editing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_user'])) {
    $username = $_POST['username'];
    $email = $_POST['email'];
    $role = $_POST['role'];
    $department = $_POST['department'];

    if (isset($_POST['user_id'])) {
        // Update existing user
        $user_id = $_POST['user_id'];
        $query = "UPDATE users SET username = ?, email = ?, role = ?, department = ? WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->execute([$username, $email, $role, $department, $user_id]);
    } else {
        // Add new user
        $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
        $query = "INSERT INTO users (username, email, password, role, department) VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($query);
        $stmt->execute([$username, $email, $password, $role, $department]);
    }
}

// Get all users
$query = "SELECT * FROM users ORDER BY id DESC";
$stmt = $conn->query($query);
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get user for editing if ID is provided
$edit_user = null;
if (isset($_GET['edit'])) {
    $user_id = $_GET['edit'];
    $query = "SELECT * FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([$user_id]);
    $edit_user = $stmt->fetch(PDO::FETCH_ASSOC);
}

// Fetch all departments for the dropdown
$departments = [];
try {
    $dept_stmt = $conn->query("SELECT name FROM departments ORDER BY name ASC");
    $departments = $dept_stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    $departments = [];
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Human Resources Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .user-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .table-responsive {
            margin-top: 20px;
        }

        /* Title styling with orange background */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            background-color: #f15b31;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: inline-block;
            min-width: 200px;
            text-align: center;
        }

        /* Button styling with orange theme */
        .btn-primary {
            background-color: #f15b31;
            border-color: #f15b31;
        }

        .btn-primary:hover {
            background-color: #d14118;
            border-color: #d14118;
        }

        /* Back to Dashboard button */
        .back-to-dashboard {
            background-color: #f15b31;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            transition: background-color 0.3s;
        }

        .back-to-dashboard:hover {
            background-color: #d14118;
            color: white;
            text-decoration: none;
        }

        /* Navbar styling with orange theme */
        .navbar {
            background-color: #f15b31 !important;
        }

        .navbar-brand {
            color: white !important;
            font-weight: bold;
        }

        .navbar-nav .nav-link {
            color: white !important;
        }

        .navbar-nav .nav-link:hover {
            color: #ffccbb !important;
        }

        .navbar-nav .nav-link.active {
            color: #ffccbb !important;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Human Resources Management System</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../user/dashboard_user.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../user/user_management.php">User Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/add_driver.php">Driver Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/departments.php">Departments Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/roles_management.php">Roles Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/add_user.php">Add User Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../finance/payroll.php">Payroll Management</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../auth/logout.php">Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-md-4">
                <div class="user-form">
                    <h3><?php echo $edit_user ? 'Edit User' : 'Add New User'; ?></h3>
                    <form method="POST" action="">
                        <?php if ($edit_user): ?>
                            <input type="hidden" name="user_id" value="<?php echo $edit_user['id']; ?>">
                        <?php endif; ?>

                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required
                                value="<?php echo $edit_user ? ($edit_user['username'] ?? '') : ''; ?>">
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required
                                value="<?php echo $edit_user ? ($edit_user['email'] ?? '') : ''; ?>">
                        </div>

                        <?php if (!$edit_user): ?>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <label for="role" class="form-label">Role</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">Select Role</option>
                                <option value="admin" <?php echo $edit_user && ($edit_user['role'] ?? '') === 'admin' ? 'selected' : ''; ?>>Admin</option>
                                <option value="finance" <?php echo $edit_user && ($edit_user['role'] ?? '') === 'finance' ? 'selected' : ''; ?>>Finance</option>
                                <option value="inventory" <?php echo $edit_user && ($edit_user['role'] ?? '') === 'inventory' ? 'selected' : ''; ?>>Inventory</option>
                                <option value="logistics" <?php echo $edit_user && ($edit_user['role'] ?? '') === 'logistics' ? 'selected' : ''; ?>>Logistics</option>
                                <option value="warehouse" <?php echo $edit_user && ($edit_user['role'] ?? '') === 'warehouse' ? 'selected' : ''; ?>>Warehouse</option>
                                <option value="payroll" <?php echo $edit_user && ($edit_user['role'] ?? '') === 'payroll' ? 'selected' : ''; ?>>Payroll</option>
                                <option value="user" <?php echo $edit_user && ($edit_user['role'] ?? '') === 'user' ? 'selected' : ''; ?>>User</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="department" class="form-label">Department</label>
                            <select class="form-select" id="department" name="department" required>
                                <option value="">Select Department</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo htmlspecialchars($dept); ?>" <?php echo ($edit_user && ($edit_user['department'] ?? '') === $dept) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($dept); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <button type="submit" name="submit_user" class="btn btn-primary">
                            <?php echo $edit_user ? 'Update User' : 'Add User'; ?>
                        </button>

                        <?php if ($edit_user): ?>
                            <a href="user_management.php" class="btn btn-secondary">Cancel</a>
                        <?php endif; ?>
                    </form>
                </div>
            </div>

            <div class="col-md-8">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Department</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?php echo $user['id']; ?></td>
                                    <td><?php echo htmlspecialchars($user['username'] ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($user['email'] ?? ''); ?></td>
                                    <td><?php echo ucfirst($user['role'] ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($user['department'] ?? ''); ?></td>
                                    <td>
                                        <a href="?edit=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="" style="display: inline;">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <button type="submit" name="delete_user" class="btn btn-sm btn-danger"
                                                onclick="return confirm('Are you sure you want to delete this user?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Dashboard Button -->
    <div class="text-center mt-4 mb-4">
        <a href="../admin/admin_dashboard.php" class="back-to-dashboard">Back to Dashboard</a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>