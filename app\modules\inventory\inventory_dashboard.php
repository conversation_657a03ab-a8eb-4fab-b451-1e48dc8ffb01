<?php
require_once '../../config/config.php';
// Set up date range variables for widgets
$report_type = 'monthly';
$date_range = date('Y-m-d');
$start_date = date('Y-m-01', strtotime($date_range));
$end_date = date('Y-m-t', strtotime($date_range));
$group_by = "DATE_FORMAT(created_at, '%Y-%m')";
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="x-icon" href="upload/eurospice-favicon.png">
    <title>Inventory Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-SgOJa3DmI69IUzQ2PVdRZhwQ+dy64/BUtbMJw1MZ8t5HZApcHrRKUc4W0kG879m7" crossorigin="anonymous">
    <link
        href="https://cdn.prod.website-files.com/66a833f537135b05bc1eaecb/css/maria-bettinas-dynamite-site.webflow.05b59e178.css"
        rel="stylesheet" type="text/css">
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background-image: url('../assets/images/eurospice-grid.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: fixed;
            min-height: 100vh;
        }

        body,
        html {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        .navbar {
            background-color: #b82720;
        }

        .log-out-btn {
            color: white;
            text-decoration: none;
            padding: 5px 10px;
        }

        .log-out-btn:hover {
            color: #ffc107;
        }

        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            font-weight: bold;
        }

        #currentdatetime {
            text-align: right;
            margin-bottom: 15px;
            font-style: italic;
        }

        .dashboard-title {
            color: white;
            margin-bottom: 20px;
            border-bottom: 2px solid white;
            padding-bottom: 10px;
        }

        /* Title styling with orange background */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            background-color: #f15b31;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: inline-block;
            min-width: 200px;
            text-align: center;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg bg-body-transparent">
        <div class="container-fluid">
            <a class="navbar-brand text-white" href="#"><img src="../finance/upload/eurospice-logo.png" alt=""></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false"
                aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link active text-white" aria-current="page" href="../product/supplier_prod.php">Supplier Table</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white" aria-current="page" href="../finance/PAR.php">Confirmation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white" aria-current="page" href="../inventory/inventory.php">Inventory</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white" aria-current="page" href="../inventory/inventory_dashboard.php">Dashboard</a>
                    </li>
                </ul>

                <div class="user-container d-flex me-auto mb-2 mb-lg-0">
                    <a class="log-out-btn" href="../auth/logout.php">Log out</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div id="currentdatetime"></div>
        <h1 class="dashboard-title">Inventory Dashboard</h1>

        <!-- Sales Overview Cards -->
        <?php
        // Get sales data directly here instead of including separate file
        try {
            $sales_sql = "SELECT
                COUNT(*) as total_orders,
                SUM(total_amount) as total_sales,
                AVG(total_amount) as average_order_value
              FROM procurement_orders
              WHERE status = 'completed'";

            $sales_stmt = $conn->prepare($sales_sql);
            $sales_stmt->execute();
            $sales_result = $sales_stmt->fetch(PDO::FETCH_ASSOC);

            $total_sales = $sales_result['total_sales'] ?? 0;
            $total_orders = $sales_result['total_orders'] ?? 0;
            $avg_order_value = $sales_result['average_order_value'] ?? 0;
        } catch (Exception $e) {
            $total_sales = 0;
            $total_orders = 0;
            $avg_order_value = 0;
            error_log("Sales query error: " . $e->getMessage());
        }
        ?>

        <style>
            .stat-card {
                background: linear-gradient(45deg, #f15b31, #d14426);
                color: white;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                margin-bottom: 20px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }

            .stat-card h6 {
                margin-bottom: 10px;
                font-size: 14px;
                opacity: 0.9;
                background: none !important;
                color: white !important;
                padding: 0 !important;
                display: block !important;
                min-width: auto !important;
            }

            .stat-card h3 {
                margin: 0;
                font-size: 28px;
                font-weight: bold;
                background: none !important;
                color: white !important;
                padding: 0 !important;
                display: block !important;
                min-width: auto !important;
            }
        </style>

        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card">
                    <h6>Total Sales</h6>
                    <h3>$<?php echo number_format($total_sales, 2); ?></h3>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <h6>Total Orders</h6>
                    <h3><?php echo number_format($total_orders); ?></h3>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <h6>Average Order Value</h6>
                    <h3>$<?php echo $total_orders > 0 ? number_format($avg_order_value, 2) : '0.00'; ?></h3>
                </div>
            </div>
        </div>

        <!-- Inventory Dashboard Content -->
        <div id="overallTotals" class="mb-3"></div>
        <div class="row">
            <div class="col-md-6">
                <div class="card" style="background-color: #f15b31; color: white;">
                    <div class="card-body">
                        <h5>Inventory by Brand (Realtime)</h5>
                        <div class="chart-container" style="height:350px;">
                            <canvas id="inventoryChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card" style="background-color: #f15b31; color: white;">
                    <div class="card-body">
                        <h5>Brand Inventory Details</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="brandInventoryTable" style="color: white;">
                                <thead>
                                    <tr>
                                        <th style="color: white;">Brand</th>
                                        <th style="color: white;">Total Products</th>
                                        <th style="color: white;">Total Stock</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="3" class="text-center">Loading...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back to Dashboard Button -->
        <div class="text-center mt-4 mb-4">
            <a href="../admin/admin_dashboard.php" class="btn" style="background-color: #f15b31; color: white;">Back to Dashboard</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
    <script>
        // Function to display the current date and time
        function updateDateTime() {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            document.getElementById('currentdatetime').textContent = now.toLocaleDateString('en-US', options);
        }

        // Update date and time when page loads
        updateDateTime();

        // Inventory Chart
        const inventoryCtx = document.getElementById('inventoryChart').getContext('2d');
        let inventoryChart;

        function fetchAndUpdateInventory() {
            console.log('🔄 Fetching inventory data...');

            fetch('../get_inventory_data.php')
                .then(response => {
                    console.log('📡 Response status:', response.status);
                    if (!response.ok) {
                        throw new Error('Network response was not ok: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('✅ Inventory data received:', data);

                    if (!data || data.length === 0) {
                        console.warn('⚠️ No inventory data found');
                        document.getElementById('overallTotals').innerHTML = '<p class="text-warning">No inventory data found</p>';
                        return;
                    }

                    const brands = data.map(item => item.brand_name);
                    const stocks = data.map(item => item.total_stock);
                    const products = data.map(item => item.total_products);

                    // Calculate overall totals
                    const totalAllStocks = stocks.reduce((a, b) => a + b, 0);
                    const totalAllProducts = products.reduce((a, b) => a + b, 0);
                    document.getElementById('overallTotals').innerHTML =
                        `<b style="color: white;">All Brands:</b> Total Products: <span class='text-warning'>${totalAllProducts}</span> &nbsp; | &nbsp; Total Stocks: <span class='text-warning'>${totalAllStocks}</span>`;

                    // Update Chart
                    if (!inventoryChart) {
                        inventoryChart = new Chart(inventoryCtx, {
                            type: 'pie',
                            data: {
                                labels: brands,
                                datasets: [{
                                    data: stocks,
                                    backgroundColor: [
                                        '#ff6b35', '#f7931e', '#ffd23f', '#06ffa5', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd'
                                    ]
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                const idx = context.dataIndex;
                                                return brands[idx] + ': ' + stocks[idx] + ' stocks, ' + products[idx] + ' products';
                                            }
                                        }
                                    },
                                    legend: {
                                        labels: {
                                            color: 'white'
                                        }
                                    }
                                }
                            }
                        });
                    } else {
                        inventoryChart.data.labels = brands;
                        inventoryChart.data.datasets[0].data = stocks;
                        inventoryChart.update();
                    }

                    // Update Table
                    const tbody = document.querySelector('#brandInventoryTable tbody');
                    tbody.innerHTML = '';
                    data.forEach(item => {
                        const tr = document.createElement('tr');
                        tr.innerHTML = `<td style="color: white;">${item.brand_name}</td><td style="color: white;">${item.total_products}</td><td style="color: white;">${item.total_stock}</td>`;
                        tbody.appendChild(tr);
                    });
                })
                .catch(error => {
                    console.error('❌ Error fetching inventory data:', error);
                    document.getElementById('overallTotals').innerHTML = '<p class="text-danger">Error loading inventory data: ' + error.message + '</p>';

                    const tbody = document.querySelector('#brandInventoryTable tbody');
                    tbody.innerHTML = '<tr><td colspan="3" class="text-center text-danger">Error loading data</td></tr>';
                });
        }

        // Load inventory data when page loads
        fetchAndUpdateInventory();

        // Refresh every 10 seconds
        setInterval(fetchAndUpdateInventory, 10000);
    </script>
</body>

</html>