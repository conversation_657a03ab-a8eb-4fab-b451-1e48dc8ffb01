<?php
session_start();
require_once '../../config/config.php';
require_once '../../includes/auth_check.php';

// Check if user is admin or finance role
if (
    !isset($_SESSION['user_id']) ||
    (isset($_SESSION['user_role']) && $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'finance')
) {
    header('Location: ../../auth/login.php');
    exit();
}

// Check if payroll ID is provided
if (!isset($_GET['id'])) {
    header('Location: payroll.php');
    exit();
}

$payroll_id = $_GET['id'];

// Get payroll record
$sql = "SELECT p.*, CONCAT(u.first_name, ' ', u.last_name) as employee_name, u.email, u.phone_number
        FROM payroll p
        JOIN users u ON p.user_id = u.id
        WHERE p.id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$payroll_id]);
$payroll = $stmt->fetch(PDO::FETCH_ASSOC);

// If payroll record not found, redirect back to payroll page
if (!$payroll) {
    header('Location: payroll.php');
    exit();
}

// Company information
$company_name = "Finance Management System";
$company_address = "3708 N Guevarra St., Zone 1 Dasmariñas, Cavite Philippines 4114";
$company_phone = "+63 ************";
$company_email = "<EMAIL>";

// Generate payslip number
$payslip_number = "PS-" . str_pad($payroll['id'], 6, "0", STR_PAD_LEFT);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payslip - <?php echo $payslip_number; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f5f5;
        }

        .payslip-container {
            max-width: 800px;
            margin: 30px auto;
            background-color: white;
            padding: 30px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        .payslip-header {
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }

        .payslip-title {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }

        .payslip-number {
            font-size: 18px;
            color: #6c757d;
        }

        .section-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #343a40;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }

        .pay-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .total-row {
            font-weight: bold;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
            margin-top: 10px;
        }

        .print-btn {
            margin-top: 20px;
        }

        @media print {
            .no-print {
                display: none;
            }

            body {
                background-color: white;
            }

            .payslip-container {
                box-shadow: none;
                margin: 0;
                padding: 15px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="payslip-container">
            <div class="payslip-header d-flex justify-content-between align-items-center">
                <div>
                    <div class="payslip-title"><?php echo $company_name; ?></div>
                    <div><?php echo $company_address; ?></div>
                    <div><?php echo $company_phone; ?> | <?php echo $company_email; ?></div>
                </div>
                <div class="text-end">
                    <div class="payslip-number"><?php echo $payslip_number; ?></div>
                    <div>Payment Date: <?php echo date('F d, Y', strtotime($payroll['payment_date'])); ?></div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <h5 class="section-title">Employee Information</h5>
                    <div><strong>Name:</strong> <?php echo htmlspecialchars($payroll['employee_name']); ?></div>
                    <div><strong>Email:</strong> <?php echo htmlspecialchars($payroll['email']); ?></div>
                    <div><strong>Phone:</strong> <?php echo htmlspecialchars($payroll['phone_number'] ?? 'N/A'); ?></div>
                </div>
                <div class="col-md-6">
                    <h5 class="section-title">Pay Period</h5>
                    <div><strong>Period:</strong> <?php echo htmlspecialchars($payroll['pay_period']); ?></div>
                    <div><strong>Status:</strong> <?php echo ucfirst($payroll['status']); ?></div>
                </div>
            </div>

            <h5 class="section-title">Earnings</h5>
            <div class="pay-details">
                <div class="row mb-2">
                    <div class="col-6">Regular Hours</div>
                    <div class="col-3 text-end"><?php echo $payroll['hours_worked']; ?> hrs</div>
                    <div class="col-3 text-end">₱<?php echo number_format($payroll['pay_rate'], 2); ?>/hr</div>
                </div>
                <div class="row total-row">
                    <div class="col-9">Gross Pay</div>
                    <div class="col-3 text-end">₱<?php echo number_format($payroll['gross_pay'], 2); ?></div>
                </div>
            </div>

            <h5 class="section-title">Deductions</h5>
            <div class="pay-details">
                <div class="row mb-2">
                    <div class="col-9">Tax & Other Deductions (12%)</div>
                    <div class="col-3 text-end">₱<?php echo number_format($payroll['deductions'], 2); ?></div>
                </div>
                <div class="row total-row">
                    <div class="col-9">Total Deductions</div>
                    <div class="col-3 text-end">₱<?php echo number_format($payroll['deductions'], 2); ?></div>
                </div>
            </div>

            <h5 class="section-title">Net Pay</h5>
            <div class="pay-details">
                <div class="row total-row">
                    <div class="col-9">Net Pay</div>
                    <div class="col-3 text-end">₱<?php echo number_format($payroll['net_pay'], 2); ?></div>
                </div>
            </div>

            <div class="mt-4 text-center">
                <p>This is a computer-generated document. No signature required.</p>
            </div>

            <div class="d-flex justify-content-between mt-4 no-print">
                <a href="payroll.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Payroll
                </a>
                <button onclick="window.print()" class="btn btn-primary print-btn">
                    <i class="fas fa-print"></i> Print Payslip
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>

</html>