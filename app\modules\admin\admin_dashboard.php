<?php

session_start();

require_once '../../config/config.php';
require_once '../../includes/auth_check.php';

// No need for use statements here as PDO is in global namespace

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../../modules/auth/login.php');
    exit();
}

// Get module statistics
function getModuleStats($conn, $module)
{
    $stats = [];
    try {
        switch ($module) {
            case 'users':
                $query = "SELECT COUNT(*) as total FROM users";
                $stmt = $conn->prepare($query);
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['total_users'] = $result['total'] ?? 0;
                break;
            case 'products':
                $query = "SELECT COUNT(*) as total FROM products";
                $stmt = $conn->prepare($query);
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['total_products'] = $result['total'] ?? 0;
                break;
            case 'inventory':
                $query = "SELECT COUNT(*) as total FROM inventory WHERE status = 'approved'";
                $stmt = $conn->prepare($query);
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['total_items'] = $result['total'] ?? 0;
                break;
                // Add more module statistics as needed
        }
    } catch (Exception $e) {
        // Return default values if there's an error
        error_log("Admin dashboard module stats error for $module: " . $e->getMessage());
        switch ($module) {
            case 'users':
                $stats['total_users'] = 0;
                break;
            case 'products':
                $stats['total_products'] = 0;
                break;
            case 'inventory':
                $stats['total_items'] = 0;
                break;
        }
    }
    return $stats;
}

$moduleStats = [
    'users' => getModuleStats($conn, 'users'),
    'products' => getModuleStats($conn, 'products'),
    'inventory' => getModuleStats($conn, 'inventory')
];

// Get sales data for admin dashboard
try {
    $sales_sql = "SELECT
        COUNT(*) as total_orders,
        SUM(total_amount) as total_sales,
        AVG(total_amount) as average_order_value
      FROM procurement_orders
      WHERE status = 'completed'";

    $sales_stmt = $conn->prepare($sales_sql);
    $sales_stmt->execute();
    $sales_result = $sales_stmt->fetch(PDO::FETCH_ASSOC);

    $total_sales = $sales_result['total_sales'] ?? 0;
    $total_orders = $sales_result['total_orders'] ?? 0;
    $avg_order_value = $sales_result['average_order_value'] ?? 0;
} catch (Exception $e) {
    $total_sales = 0;
    $total_orders = 0;
    $avg_order_value = 0;
    error_log("Admin dashboard sales query error: " . $e->getMessage());
}

// Set up date range variables for widgets
$report_type = 'monthly';
$date_range = date('Y-m-d');
$start_date = date('Y-m-01', strtotime($date_range));
$end_date = date('Y-m-t', strtotime($date_range));
$group_by = "DATE_FORMAT(created_at, '%Y-%m')";
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Euro Spice | Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="icon" type="x-icon" href="<?php echo BASE_URL; ?>/assets/images/eurospice-favicon.png">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #F15B31;
        }

        .space {
            height: 54px;
            width: 100%;
        }

        .body-container {
            padding: 10px;
        }

        #sidebar-nav {
            display: flex;
            flex-direction: column;
        }

        #sidebar-nav a {
            text-decoration: none;
            color: white;
            padding: 10px;
            background-color: #F15B31;
            transition: background-color 0.3s ease;
            width: 100%;
        }

        #sidebar-nav a:hover {
            background-color: #D14118;
        }

        .offcanvas-body {
            background-color: #F15B31;
            padding: 0;
        }

        .offcanvas-header {
            background-color: #F15B31;
            color: white;
        }

        #navbar-container {
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            padding: 5px;
            border-radius: 15px;
            width: 90%;
        }

        .container-me {
            background-color: #faf2e9;
            width: 100%;
            height: 100vh;
            border-radius: 5px;
            padding: 20px;
            overflow-y: auto;
        }

        .bg-primary {
            background-color: #D14118 !important;
        }

        #open-sidebar {
            text-decoration: none;
            color: white;
            font-weight: 800;
            font-size: 2rem;
            margin-left: 10px;
            margin-right: 10px;
        }

        .dropdown {
            margin-right: 10px;
        }

        .module-card {
            transition: transform 0.2s;
            margin-bottom: 20px;
            height: 100%;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .module-card:hover {
            transform: translateY(-5px);
        }

        .stats-card {
            background-color: #D14118;
            color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .row-equal {
            display: flex;
            flex-wrap: wrap;
        }

        .row-equal>[class*='col-'] {
            display: flex;
            flex-direction: column;
            margin-bottom: 30px;
        }

        .card {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 1.5rem;
        }

        .card-title {
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .card-text {
            margin-bottom: 1.5rem;
        }

        .list-group {
            margin-top: auto;
        }
    </style>
</head>

<body>
    <div class="space"></div>
    <section class="body-container">
        <!-- Offcanvas Sidebar -->
        <div class="offcanvas offcanvas-start" tabindex="-1" id="offcanvas" aria-labelledby="offcanvasLabel">
            <img src="<?php echo BASE_URL; ?>/assets/images/eurospice-logo.png" alt="Euro Spice Logo" width="100%">
            <div class="offcanvas-header">
                <h5 class="offcanvas-title" id="offcanvasLabel">Welcome <?php echo $_SESSION['username'] ?? 'User'; ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body">
                <nav id="sidebar-nav">
                    <a href="admin_dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                    <a href="../user/add_user.php"><i class="fas fa-user-plus me-2"></i>Add User</a>
                    <a href="../user/roles_management.php"><i class="fas fa-user-shield me-2"></i>Manage Roles</a>
                    <a href="../user/departments.php"><i class="fas fa-building me-2"></i>Departments</a>
                    <a href="../user/add_driver.php"><i class="fas fa-truck-moving me-2"></i>Manage Drivers</a>
                    <a href="../finance/financial_reports.php"><i class="fas fa-file-invoice-dollar me-2"></i>Finance</a>
                    <a href="../inventory/inventory.php"><i class="fas fa-warehouse me-2"></i>Inventory</a>
                    <a href="../logistics/dashboard_logistics.php"><i class="fas fa-truck me-2"></i>Logistics</a>
                    <a href="../procurement/procurement.php?action=list"><i class="fas fa-shopping-cart me-2"></i>Procurement</a>
                    <a href="../reports/reports.php?type=all"><i class="fas fa-chart-bar me-2"></i>Reports</a>
                    <a href="<?php echo BASE_URL; ?>/ecommerce_complete/pages/account/login.php?force=1"><i class="fas fa-cash-register me-2"></i>POS Login</a>
                </nav>
            </div>
        </div>

        <div class="navbar navbar-dark bg-primary" id="navbar-container">
            <a class="sidebar-emoji" id="open-sidebar" data-bs-toggle="offcanvas" href="#offcanvas" role="button" aria-controls="offcanvas">
                ☰
            </a>
            <div class="dropdown" id="user-settings">
                <a class="dropdown-toggle text-white text-decoration-none" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    👤
                </a>
                <ul class="dropdown-menu" id="user-dropdown" aria-labelledby="userDropdown">
                    <li><a class="dropdown-item" href="../user/profile.php"><i class="fas fa-user-edit me-2"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="../user/profile.php?view=security"><i class="fas fa-lock me-2"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logout(); return false;"><i class="fas fa-sign-out-alt me-2"></i>Log Out</a></li>
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/ecommerce_complete/pages/account/login.php?force=1" class="list-group-item list-group-item-action">
                            <i class="fas fa-cash-register"></i> POS Login</li>
                    </a>
                </ul>
            </div>
        </div>

        <div class="container-me">
            <!-- Quick Stats -->
            <div class="row row-equal mb-4 mt-4">
                <div class="col-md-3">
                    <div class="card stats-card">
                        <div class="card-body">
                            <h5 class="card-title">Total Users</h5>
                            <h2><?php echo $moduleStats['users']['total_users'] ?? 0; ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stats-card">
                        <div class="card-body">
                            <h5 class="card-title">Total Products</h5>
                            <h2><?php echo $moduleStats['products']['total_products'] ?? 0; ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stats-card">
                        <div class="card-body">
                            <h5 class="card-title">Inventory Items</h5>
                            <h2><?php echo $moduleStats['inventory']['total_items'] ?? 0; ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stats-card">
                        <div class="card-body">
                            <h5 class="card-title">Total Orders</h5>
                            <h2><?php echo number_format($total_orders); ?></h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales Overview -->
            <div class="row row-equal mb-4">
                <div class="col-md-4">
                    <div class="card stats-card">
                        <div class="card-body">
                            <h5 class="card-title">Total Sales</h5>
                            <h2>$<?php echo number_format($total_sales, 2); ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card stats-card">
                        <div class="card-body">
                            <h5 class="card-title">Average Order Value</h5>
                            <h2>$<?php echo $total_orders > 0 ? number_format($avg_order_value, 2) : '0.00'; ?></h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card stats-card">
                        <div class="card-body">
                            <h5 class="card-title">System Status</h5>
                            <h2><?php echo ($total_sales > 0 && $moduleStats['inventory']['total_items'] > 0) ? '✅ Active' : '⚠️ Setup'; ?></h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Module Overview -->
            <div class="row row-equal">
                <!-- User Management -->
                <div class="col-md-4">
                    <div class="card module-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-users"></i> Human Resources Management</h5>
                            <p class="card-text">Manage system users, roles, departments, and drivers</p>
                            <div class="list-group">
                                <a href="../user/add_user.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-user-plus"></i> Add New User
                                </a>
                                <a href="../user/roles_management.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-user-shield"></i> Manage Roles
                                </a>
                                <a href="../user/departments.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-building"></i> Manage Departments
                                </a>
                                <a href="../user/add_driver.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-truck-moving"></i> Manage Drivers
                                </a>
                                <a href="../user/dashboard_user.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-chart-bar"></i> User Dashboard
                                </a>
                                <a href="../finance/payroll.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-money-check-alt"></i> Payroll System
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Management -->
                <div class="col-md-4">
                    <div class="card module-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-warehouse"></i> Inventory Management</h5>
                            <p class="card-text">Track inventory levels and stock movements</p>
                            <div class="list-group">
                                <a href="../inventory/inventory_dashboard.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-chart-bar"></i> Inventory Reports
                                </a>
                                <a href="../inventory/inventory.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-table"></i> Inventory Table
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Finance Management -->
                <div class="col-md-4">
                    <div class="card module-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-money-bill-wave"></i> Finance Management</h5>
                            <p class="card-text">Manage budgets, expenses, and financial reports</p>
                            <div class="list-group">
                                <a href="../finance/PAR.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-chart-line"></i> Confirmation of Orders
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Logistics Management -->
                <div class="col-md-4">
                    <div class="card module-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-truck"></i> Logistics Management</h5>
                            <p class="card-text">Track deliveries and manage logistics operations</p>
                            <div class="list-group">
                                <a href="../logistics/dashboard_logistics.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-chart-bar"></i> Logistics Dashboard
                                </a>
                                <a href="../logistics/track_deliveries.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-truck-loading"></i> Track Deliveries
                                </a>
                                <a href="../logistics/logistics_management.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-user-tie"></i> Manage Drivers
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Procurement Management -->
                <div class="col-md-4">
                    <div class="card module-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-shopping-cart"></i> Procurement Management</h5>
                            <p class="card-text">Manage procurement orders and requests</p>
                            <div class="list-group">
                                <a href="../procurement/procurement.php?action=new" class="list-group-item list-group-item-action">
                                    <i class="fas fa-plus"></i> New Order
                                </a>
                                <a href="../procurement/procurement.php?action=list" class="list-group-item list-group-item-action">
                                    <i class="fas fa-list"></i> View Orders
                                </a>
                                <a href="../procurement/suppliers.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-truck"></i> Manage Suppliers
                                </a>
                                <a href="../procurement/spices.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-pepper-hot"></i> Manage Categories
                                </a>
                                <a href="../procurement/brands.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-chart-bar"></i> Manage Brands
                                </a>
                                <a href="../procurement/measurement_units.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-ruler"></i> Measurement Units
                                </a>
                                <a href="../procurement/pack_types.php" class="list-group-item list-group-item-action">
                                    <i class="fas fa-box"></i> Package Types
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports -->
                <div class="col-md-4">
                    <div class="card module-card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-chart-bar"></i> Reports</h5>
                            <p class="card-text">View and generate system reports</p>
                            <div class="list-group">
                                <a href="../reports/reports.php?type=sales" class="list-group-item list-group-item-action">
                                    <i class="fas fa-chart-line"></i> Sales Reports
                                </a>
                                <a href="../reports/reports.php?type=inventory" class="list-group-item list-group-item-action">
                                    <i class="fas fa-boxes"></i> Inventory Reports
                                </a>
                                <a href="../reports/reports.php?type=finance" class="list-group-item list-group-item-action">
                                    <i class="fas fa-file-invoice-dollar"></i> Financial Reports
                                </a>
                                <a href="../reports/reports.php?type=all" class="list-group-item list-group-item-action">
                                    <i class="fas fa-list"></i> All Reports
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Logout function with notification
        function logout() {
            // Show confirmation dialog
            if (confirm("Are you sure you want to log out?")) {
                // Create notification element
                const notification = document.createElement('div');
                notification.style.position = 'fixed';
                notification.style.top = '20px';
                notification.style.left = '50%';
                notification.style.transform = 'translateX(-50%)';
                notification.style.backgroundColor = '#D14118';
                notification.style.color = 'white';
                notification.style.padding = '15px 25px';
                notification.style.borderRadius = '5px';
                notification.style.zIndex = '9999';
                notification.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
                notification.textContent = 'Logging you out...';

                // Add notification to body
                document.body.appendChild(notification);

                // Set timeout to redirect after showing notification
                setTimeout(function() {
                    window.location.href = '<?php echo BASE_URL; ?>/app/modules/auth/login.php';
                }, 1500); // Redirect after 1.5 seconds
            }
        }
    </script>
</body>

</html>