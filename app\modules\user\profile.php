<?php
session_start();
require_once '../../config/config.php';
require_once '../../includes/auth_check.php';

$user_id = $_SESSION['user_id'];
$query = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($query);
$stmt->execute([$user_id]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

/** @var PDO $conn */

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Finance Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Title styling with orange background */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            background-color: #f15b31;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: inline-block;
            min-width: 200px;
            text-align: center;
        }

        /* Card title styling */
        .card-title {
            background-color: transparent !important;
            color: #333 !important;
            padding: 0 !important;
            margin: 0 0 1rem 0 !important;
            display: block !important;
            text-align: left !important;
        }

        /* Back to Dashboard button */
        .back-to-dashboard {
            background-color: #f15b31;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            transition: background-color 0.3s;
        }

        .back-to-dashboard:hover {
            background-color: #d14118;
            color: white;
            text-decoration: none;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Human Resources Management System</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../user/dashboard_user.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../user/user_management.php">User Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/add_driver.php">Driver Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/departments.php">Departments Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/roles_management.php">Roles Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/add_user.php">Add User Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../finance/payroll.php">Payroll Management</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../auth/logout.php">Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <div class="container mt-5">
        <h2>User Profile</h2>
        <div class="card" style="max-width: 500px;">
            <div class="card-body">
                <h5 class="card-title mb-3"><?php echo htmlspecialchars($user['email']); ?></h5>
                <ul class="list-group list-group-flush">
                    <li class="list-group-item"><strong>Department:</strong> <?php echo htmlspecialchars($user['department']); ?></li>
                    <li class="list-group-item"><strong>Role:</strong> <?php echo htmlspecialchars($user['role'] ?? ''); ?></li>
                    <li class="list-group-item"><strong>Created At:</strong> <?php echo htmlspecialchars($user['created_at'] ?? ''); ?></li>
                </ul>
            </div>
        </div>
        <a href="../auth/logout.php" class="btn btn-danger mt-3">Logout</a>

        <!-- Back to Dashboard Button -->
        <div class="text-center mt-4 mb-4">
            <a href="../admin/admin_dashboard.php" class="back-to-dashboard">Back to Dashboard</a>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>