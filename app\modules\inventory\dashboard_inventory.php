<?php
require_once '../../config/config.php';
?>
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Inventory Management Dashboard</h2>
    </div>
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <h5 class="card-title">All Brands: Total Products</h5>
                    <h2 id="totalProductsCard">0</h2>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <h5 class="card-title">All Brands: Total Stocks</h5>
                    <h2 id="totalStocksCard">0</h2>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <div class="dashboard-card p-3">
                <h5>Inventory by Brand (Realtime)</h5>
                <div class="chart-container" style="height:350px;">
                    <canvas id="inventoryChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="dashboard-card p-3">
                <h5>Brand Inventory Details</h5>
                <div class="table-responsive">
                    <table class="table table-bordered" id="brandInventoryTable">
                        <thead>
                            <tr>
                                <th>Brand</th>
                                <th>Total Products</th>
                                <th>Total Stock</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Filled by JS -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- Low Stock Items Table -->
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="dashboard-card p-3">
                <h5>Low Stock Items (Stock &lt; 10)</h5>
                <div class="table-responsive">
                    <table class="table table-bordered" id="lowStockTable">
                        <thead>
                            <tr>
                                <th>Product Name</th>
                                <th>Brand</th>
                                <th>Stock</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Filled by JS -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Dashboard Button -->
    <div class="text-center mt-4 mb-4">
        <a href="../admin/admin_dashboard.php" class="btn" style="background-color: #f15b31; color: white;">Back to Dashboard</a>
    </div>
</div>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
<style>
    .stats-card {
        background: linear-gradient(45deg, #4b6cb7, #182848);
        color: white;
        margin-bottom: 20px;
        border-radius: 10px;
    }

    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
</style>
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
<script>
    const inventoryCtx = document.getElementById('inventoryChart').getContext('2d');
    let inventoryChart;

    function fetchAndUpdateInventory() {
        fetch('../get_inventory_data.php')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                console.log('Inventory data received:', data);
                const brands = data.map(item => item.brand_name);
                const stocks = data.map(item => item.total_stock);
                const products = data.map(item => item.total_products);

                // Calculate overall totals
                const totalAllStocks = stocks.reduce((a, b) => a + b, 0);
                const totalAllProducts = products.reduce((a, b) => a + b, 0);
                document.getElementById('totalProductsCard').textContent = totalAllProducts;
                document.getElementById('totalStocksCard').textContent = totalAllStocks;

                // Update Chart
                if (!inventoryChart) {
                    inventoryChart = new Chart(inventoryCtx, {
                        type: 'pie',
                        data: {
                            labels: brands,
                            datasets: [{
                                data: stocks,
                                backgroundColor: [
                                    '#4b6cb7', '#182848', '#2c3e50', '#3498db', '#2980b9', '#e67e22', '#16a085', '#8e44ad', '#c0392b'
                                ]
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            const idx = context.dataIndex;
                                            return brands[idx] + ': ' + stocks[idx] + ' stocks, ' + products[idx] + ' products';
                                        }
                                    }
                                }
                            }
                        }
                    });
                } else {
                    inventoryChart.data.labels = brands;
                    inventoryChart.data.datasets[0].data = stocks;
                    inventoryChart.update();
                }

                // Update Table
                const tbody = document.querySelector('#brandInventoryTable tbody');
                tbody.innerHTML = '';
                data.forEach(item => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `<td>${item.brand_name}</td><td>${item.total_products}</td><td>${item.total_stock}</td>`;
                    tbody.appendChild(tr);
                });
            })
            .catch(error => {
                console.error('Error fetching inventory data:', error);
                // Show error message in the chart area
                document.getElementById('totalProductsCard').textContent = 'Error';
                document.getElementById('totalStocksCard').textContent = 'Error';
            });

        // Fetch low stock items
        fetch('../get_low_stock.php')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                console.log('Low stock data received:', data);
                const tbody = document.querySelector('#lowStockTable tbody');
                tbody.innerHTML = '';
                if (data.length > 0) {
                    data.forEach(item => {
                        const tr = document.createElement('tr');
                        tr.innerHTML = `<td>${item.name}</td><td>${item.brand_name}</td><td>${item.stock}</td>`;
                        tbody.appendChild(tr);
                    });
                } else {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `<td colspan='3' class='text-center'>No low stock items</td>`;
                    tbody.appendChild(tr);
                }
            })
            .catch(error => {
                console.error('Error fetching low stock data:', error);
                const tbody = document.querySelector('#lowStockTable tbody');
                tbody.innerHTML = '<tr><td colspan="3" class="text-center text-danger">Error loading low stock data</td></tr>';
            });
    }

    fetchAndUpdateInventory();
    setInterval(fetchAndUpdateInventory, 10000);
</script>