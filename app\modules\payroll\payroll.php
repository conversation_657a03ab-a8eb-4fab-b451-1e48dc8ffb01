<?php
require_once '../../config/config.php';
require_once '../../includes/header.php';
?>

<style>
    /* Title styling with orange background */
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        background-color: #f15b31;
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        display: inline-block;
        min-width: 200px;
        text-align: center;
    }

    /* Button styling with orange theme */
    .btn-primary {
        background-color: #f15b31;
        border-color: #f15b31;
    }

    .btn-primary:hover {
        background-color: #d14118;
        border-color: #d14118;
    }

    /* Card header styling */
    .card-header h4 {
        background-color: transparent;
        color: #333;
        padding: 0;
        margin: 0;
        display: block;
        text-align: left;
    }

    /* Back to Dashboard button */
    .back-to-dashboard {
        background-color: #f15b31;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        text-decoration: none;
        display: inline-block;
        margin-bottom: 20px;
        transition: background-color 0.3s;
    }

    .back-to-dashboard:hover {
        background-color: #d14118;
        color: white;
        text-decoration: none;
    }
</style>

<div class="container mt-4">
    <h2>Payroll Management</h2>
    <div class="card">
        <div class="card-header">
            <h4>Employee Payroll</h4>
        </div>
        <div class="card-body">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Department</th>
                        <th>Pay Per Hour</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $sql = "SELECT id, name, department, pay_per_hour FROM employees";
                    $result = $conn->query($sql);
                    if ($result && $result->rowCount() > 0) {
                        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
                            echo "<td>" . htmlspecialchars($row['department']) . "</td>";
                            echo "<td>$" . htmlspecialchars($row['pay_per_hour']) . "</td>";
                            echo "<td><a href='update_payroll_status.php?id=" . $row['id'] . "' class='btn btn-primary btn-sm'>Update</a></td>";
                            echo "</tr>";
                        }
                    } else {
                        echo "<tr><td colspan='5'>No employees found.</td></tr>";
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Back to Dashboard Button -->
    <div class="text-center mt-4 mb-4">
        <a href="../admin/admin_dashboard.php" class="back-to-dashboard">Back to Dashboard</a>
    </div>
</div>

<?php require_once '../../includes/footer.php'; ?>