<?php
session_start();
require_once '../../config/config.php';
require_once '../../includes/auth_check.php';

// Check if user is admin or finance role
if (
    !isset($_SESSION['user_id']) ||
    (isset($_SESSION['user_role']) && $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'finance')
) {
    header('Location: ../../auth/login.php');
    exit();
}

$success_message = '';
$error_message = '';

// Get all users for dropdown
$users_sql = "SELECT id, last_name, email FROM users ORDER BY last_name";
$users = $conn->query($users_sql)->fetchAll(PDO::FETCH_ASSOC);

// Get all payroll records
$payroll_records = [];
try {
    $payroll_sql = "SELECT p.*, CONCAT(u.first_name, ' ', u.last_name) as employee_name
                   FROM payroll p
                   JOIN users u ON p.user_id = u.id
                   ORDER BY p.pay_period DESC, employee_name";
    $payroll_records = $conn->query($payroll_sql)->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Table might not exist yet
    $error_message = "Initializing payroll system...";

    // Create payroll table if it doesn't exist
    $create_table_sql = "CREATE TABLE IF NOT EXISTS payroll (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        pay_rate DECIMAL(10,2) NOT NULL,
        hours_worked DECIMAL(10,2) NOT NULL,
        gross_pay DECIMAL(10,2) NOT NULL,
        deductions DECIMAL(10,2) NOT NULL,
        net_pay DECIMAL(10,2) NOT NULL,
        pay_period VARCHAR(20) NOT NULL,
        payment_date DATE NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )";
    $conn->exec($create_table_sql);
}

// Handle form submission for adding/updating payroll
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_payroll'])) {
    $user_id = $_POST['user_id'];
    $pay_rate = $_POST['pay_rate'];
    $hours_worked = $_POST['hours_worked'];
    $pay_period = $_POST['pay_period'];
    $payment_date = $_POST['payment_date'];

    // Calculate pay
    $gross_pay = $pay_rate * $hours_worked;
    $deductions = $gross_pay * 0.12; // 12% for taxes and other deductions
    $net_pay = $gross_pay - $deductions;

    // Insert new payroll record
    $sql = "INSERT INTO payroll (user_id, pay_rate, hours_worked, gross_pay, deductions, net_pay, pay_period, payment_date, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')";
    $stmt = $conn->prepare($sql);
    $result = $stmt->execute([$user_id, $pay_rate, $hours_worked, $gross_pay, $deductions, $net_pay, $pay_period, $payment_date]);

    if ($result) {
        $success_message = "Payroll record added successfully!";
        // Refresh payroll records
        $payroll_records = $conn->query($payroll_sql)->fetchAll(PDO::FETCH_ASSOC);
    } else {
        $error_message = "Error adding payroll record.";
    }
}

// Handle payroll processing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['process_payroll'])) {
    $payroll_id = $_POST['payroll_id'];

    $sql = "UPDATE payroll SET status = 'processed' WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $result = $stmt->execute([$payroll_id]);

    if ($result) {
        $success_message = "Payroll processed successfully!";
        // Refresh payroll records
        $payroll_records = $conn->query($payroll_sql)->fetchAll(PDO::FETCH_ASSOC);
    } else {
        $error_message = "Error processing payroll.";
    }
}

// Get current pay period (1st-15th or 16th-end of month)
$current_day = date('j');
$current_month = date('F');
$current_year = date('Y');

if ($current_day <= 15) {
    $default_pay_period = "1st-15th $current_month $current_year";
    $default_payment_date = date('Y-m-15');
} else {
    $default_pay_period = "16th-" . date('t') . " $current_month $current_year";
    $default_payment_date = date('Y-m-t'); // Last day of current month
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payroll System - Finance Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .payroll-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .table-responsive {
            margin-top: 20px;
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 0.9em;
        }

        .status-pending {
            background-color: #ffc107;
            color: black;
        }

        .status-processed {
            background-color: #28a745;
            color: white;
        }

        /* Title styling with orange background */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            background-color: #f15b31;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: inline-block;
            min-width: 200px;
            text-align: center;
        }

        /* Button styling with orange theme */
        .btn-primary {
            background-color: #f15b31;
            border-color: #f15b31;
        }

        .btn-primary:hover {
            background-color: #d14118;
            border-color: #d14118;
        }

        /* Card header styling */
        .card-header h4 {
            background-color: transparent;
            color: #333;
            padding: 0;
            margin: 0;
            display: block;
            text-align: left;
        }

        /* Navbar styling with orange theme */
        .navbar {
            background-color: #f15b31 !important;
        }

        .navbar-brand {
            color: white !important;
            font-weight: bold;
        }

        .navbar-nav .nav-link {
            color: white !important;
        }

        .navbar-nav .nav-link:hover {
            color: #ffccbb !important;
        }

        .navbar-nav .nav-link.active {
            color: #ffccbb !important;
            font-weight: bold;
        }

        /* Back to Dashboard button */
        .back-to-dashboard {
            background-color: #f15b31;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            transition: background-color 0.3s;
        }

        .back-to-dashboard:hover {
            background-color: #d14118;
            color: white;
            text-decoration: none;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Human Resources Management System</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../user/dashboard_user.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../user/user_management.php">User Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/add_driver.php">Driver Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/departments.php">Departments Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/roles_management.php">Roles Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/add_user.php">Add User Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../finance/payroll.php">Payroll Management</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../auth/logout.php">Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <div class="form-container">
            <h2 class="text-center mb-4">Payroll Management</h2>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h4>Add Payroll Entry</h4>
                        </div>
                        <div class="card-body payroll-form">
                            <form method="POST" action="">
                                <div class="mb-3">
                                    <label for="user_id" class="form-label">Select Employee</label>
                                    <select class="form-control" id="user_id" name="user_id" required>
                                        <option value="">-- Select Employee --</option>
                                        <?php foreach ($users as $user): ?>
                                            <option value="<?php echo $user['id']; ?>">
                                                <?php echo htmlspecialchars($user['full_name']); ?>
                                                (<?php echo htmlspecialchars($user['email']); ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="pay_rate" class="form-label">Pay Rate (per hour)</label>
                                    <div class="input-group">
                                        <span class="input-group-text">₱</span>
                                        <input type="number" class="form-control" id="pay_rate" name="pay_rate" value="300" step="0.01" min="0" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="hours_worked" class="form-label">Hours Worked (15 days)</label>
                                    <input type="number" class="form-control" id="hours_worked" name="hours_worked" value="120" step="0.5" min="0" required>
                                    <small class="form-text text-muted">Default: 8 hours/day × 15 days = 120 hours</small>
                                </div>

                                <div class="mb-3">
                                    <label for="pay_period" class="form-label">Pay Period</label>
                                    <input type="text" class="form-control" id="pay_period" name="pay_period" value="<?php echo $default_pay_period; ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="payment_date" class="form-label">Payment Date</label>
                                    <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?php echo $default_payment_date; ?>" required>
                                </div>

                                <button type="submit" name="submit_payroll" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Add Payroll Entry
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h4>Payroll Records</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Employee</th>
                                            <th>Pay Period</th>
                                            <th>Hours</th>
                                            <th>Rate</th>
                                            <th>Gross Pay</th>
                                            <th>Deductions</th>
                                            <th>Net Pay</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (count($payroll_records) > 0): ?>
                                            <?php foreach ($payroll_records as $record): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($record['employee_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($record['pay_period']); ?></td>
                                                    <td><?php echo $record['hours_worked']; ?></td>
                                                    <td>₱<?php echo number_format($record['pay_rate'], 2); ?></td>
                                                    <td>₱<?php echo number_format($record['gross_pay'], 2); ?></td>
                                                    <td>₱<?php echo number_format($record['deductions'], 2); ?></td>
                                                    <td>₱<?php echo number_format($record['net_pay'], 2); ?></td>
                                                    <td>
                                                        <span class="status-badge status-<?php echo $record['status']; ?>">
                                                            <?php echo ucfirst($record['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php if ($record['status'] === 'pending'): ?>
                                                            <form method="POST" style="display: inline;">
                                                                <input type="hidden" name="payroll_id" value="<?php echo $record['id']; ?>">
                                                                <button type="submit" name="process_payroll" class="btn btn-sm btn-success">
                                                                    <i class="fas fa-check"></i> Process
                                                                </button>
                                                            </form>
                                                        <?php else: ?>
                                                            <a href="payslip.php?id=<?php echo $record['id']; ?>" class="btn btn-sm btn-info">
                                                                <i class="fas fa-file-invoice-dollar"></i> Payslip
                                                            </a>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="9" class="text-center">No payroll records found.</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Back to Dashboard Button -->
        <div class="text-center mt-4 mb-4">
            <a href="../admin/admin_dashboard.php" class="back-to-dashboard">Back to Dashboard</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>