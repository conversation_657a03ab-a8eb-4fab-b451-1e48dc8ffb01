<?php
session_start();
require_once 'config.php';

/** @var PDO $conn */

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

$user_id = $_SESSION['user_id'];

// Get user information
$sql = "SELECT u.*, r.name as role_name, d.name as department_name
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.id
        LEFT JOIN departments d ON u.department_id = d.id
        WHERE u.id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$user_id]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

// Get all roles for dropdown
$roles_sql = "SELECT id, name FROM roles";
$roles_stmt = $conn->query($roles_sql);
$roles_result = $roles_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all departments for dropdown
$departments_sql = "SELECT id, name FROM departments";
$departments_stmt = $conn->query($departments_sql);
$departments_result = $departments_stmt->fetchAll(PDO::FETCH_ASSOC);

// Process profile update
if (isset($_POST['update_profile'])) {
    $first_name = $_POST['first_name'];
    $last_name = $_POST['last_name'];
    $birthday = $_POST['birthday'];
    $pay_per_hour = (float)$_POST['pay_per_hour'];
    $role_id = (int)$_POST['role_id'];
    $department_id = (int)$_POST['department_id'];
    $email = $_POST['email'];

    // Handle profile image upload
    $image = $user['image']; // Keep existing image by default
    if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB

        if (in_array($_FILES['profile_image']['type'], $allowed_types) && $_FILES['profile_image']['size'] <= $max_size) {
            $upload_dir = "uploads/";
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            $file_name = time() . '_' . basename($_FILES['profile_image']['name']);
            $target_file = $upload_dir . $file_name;

            if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $target_file)) {
                // Delete old image if exists
                if ($user['image'] && file_exists($upload_dir . $user['image'])) {
                    unlink($upload_dir . $user['image']);
                }
                $image = $file_name;
            }
        }
    }

    // Update user information
    $sql = "UPDATE users SET
            first_name = ?,
            last_name = ?,
            birthday = ?,
            pay_per_hour = ?,
            role_id = ?,
            department_id = ?,
            email = ?,
            image = ?
            WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $success = $stmt->execute([
        $first_name,
        $last_name,
        $birthday,
        $pay_per_hour,
        $role_id,
        $department_id,
        $email,
        $image,
        $user_id
    ]);

    if ($success) {
        $success_message = "Profile updated successfully";
        // Refresh user data
        $stmt = $conn->prepare("SELECT u.*, r.name as role_name, d.name as department_name FROM users u LEFT JOIN roles r ON u.role_id = r.id LEFT JOIN departments d ON u.department_id = d.id WHERE u.id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        $error_message = "Error updating profile.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
    <style>
        .profile-image {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
        }

        .profile-header {
            background-color: #f8f9fa;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">User Management System</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <div class="profile-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2 text-center">
                    <img src="<?php echo $user['image'] ? 'uploads/' . htmlspecialchars($user['image']) : 'https://via.placeholder.com/150'; ?>"
                        alt="Profile Image" class="profile-image">
                </div>
                <div class="col-md-10">
                    <h1><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h1>
                    <p class="text-muted">
                        <?php echo htmlspecialchars($user['role_name']); ?> |
                        <?php echo htmlspecialchars($user['department_name']); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success">
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Profile Information</h4>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                            Edit Profile
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">First Name:</div>
                            <div class="col-md-8"><?php echo htmlspecialchars($user['first_name']); ?></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Last Name:</div>
                            <div class="col-md-8"><?php echo htmlspecialchars($user['last_name']); ?></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Email:</div>
                            <div class="col-md-8"><?php echo htmlspecialchars($user['email']); ?></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Birthday:</div>
                            <div class="col-md-8"><?php echo htmlspecialchars($user['birthday']); ?></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Pay per Hour:</div>
                            <div class="col-md-8">$<?php echo number_format($user['pay_per_hour'], 2); ?></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Role:</div>
                            <div class="col-md-8"><?php echo htmlspecialchars($user['role_name']); ?></div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">Department:</div>
                            <div class="col-md-8"><?php echo htmlspecialchars($user['department_name']); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Profile Modal -->
    <div class="modal fade" id="editProfileModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Profile</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form method="POST" action="" enctype="multipart/form-data">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="first_name" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="first_name" name="first_name"
                                    value="<?php echo htmlspecialchars($user['first_name']); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="last_name" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="last_name" name="last_name"
                                    value="<?php echo htmlspecialchars($user['last_name']); ?>" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email"
                                    value="<?php echo htmlspecialchars($user['email']); ?>" required
                                    pattern="[^@]+@eurospice\.ph$"
                                    title="Email must be from eurospice.ph domain">
                            </div>
                            <div class="col-md-6">
                                <label for="birthday" class="form-label">Birthday</label>
                                <input type="date" class="form-control" id="birthday" name="birthday"
                                    value="<?php echo htmlspecialchars($user['birthday']); ?>" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="pay_per_hour" class="form-label">Pay per Hour</label>
                                <input type="number" step="0.01" class="form-control" id="pay_per_hour" name="pay_per_hour"
                                    value="<?php echo htmlspecialchars($user['pay_per_hour']); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="role_id" class="form-label">Role</label>
                                <select class="form-select" id="role_id" name="role_id" required>
                                    <?php
                                    foreach ($roles_result as $role) {
                                        $selected = ($role['id'] == $user['role_id']) ? 'selected' : '';
                                        echo "<option value='" . $role['id'] . "' $selected>" .
                                            htmlspecialchars($role['name']) . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="department_id" class="form-label">Department</label>
                                <select class="form-select" id="department_id" name="department_id" required>
                                    <?php
                                    foreach ($departments_result as $dept) {
                                        $selected = ($dept['id'] == $user['department_id']) ? 'selected' : '';
                                        echo "<option value='" . $dept['id'] . "' $selected>" .
                                            htmlspecialchars($dept['name']) . "</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="profile_image" class="form-label">Profile Image</label>
                                <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" name="update_profile" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Dashboard Button -->
    <div class="text-center mt-4 mb-4">
        <a href="../admin/admin_dashboard.php" class="btn" style="background-color: #f15b31; color: white;">Back to Dashboard</a>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
</body>

</html>