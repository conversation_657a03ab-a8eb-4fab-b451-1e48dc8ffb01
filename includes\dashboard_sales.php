<?php
// Sales Overview Widget

// Check if required variables are defined, otherwise set defaults
if (!isset($group_by)) {
    $group_by = "DATE_FORMAT(created_at, '%Y-%m')";
}

if (!isset($start_date)) {
    $start_date = date('Y-m-01'); // First day of current month
}

if (!isset($end_date)) {
    $end_date = date('Y-m-t'); // Last day of current month
}

try {
    // First try to get ALL completed orders (ignore date range for now)
    $sales_sql = "SELECT
        COUNT(*) as total_orders,
        SUM(total_amount) as total_sales,
        AVG(total_amount) as average_order_value
      FROM procurement_orders
      WHERE status = 'completed'";

    $sales_stmt = $conn->prepare($sales_sql);
    $sales_stmt->execute();
    $result = $sales_stmt->fetch(PDO::FETCH_ASSOC);

    // Convert to the expected format
    if ($result && $result['total_orders'] > 0) {
        $sales_data = [$result]; // Put in array format expected by the display code
    } else {
        $sales_data = [];
    }
} catch (Exception $e) {
    // Handle query error
    $sales_data = [];
    error_log("Sales dashboard error: " . $e->getMessage());
}
?>
<style>
    .stat-card {
        background: linear-gradient(45deg, #f15b31, #d14426);
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        margin-bottom: 20px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .stat-card h6 {
        margin-bottom: 10px;
        font-size: 14px;
        opacity: 0.9;
        background: none;
        color: white;
        padding: 0;
        display: block;
        min-width: auto;
    }

    .stat-card h3 {
        margin: 0;
        font-size: 28px;
        font-weight: bold;
        background: none;
        color: white;
        padding: 0;
        display: block;
        min-width: auto;
    }
</style>
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stat-card">
            <h6>Total Sales</h6>
            <h3>$<?php
                    $total = 0;
                    foreach ($sales_data as $row) {
                        $total += $row['total_sales'];
                    }
                    echo number_format($total, 2);
                    ?></h3>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <h6>Total Orders</h6>
            <h3><?php
                $orders = 0;
                foreach ($sales_data as $row) {
                    $orders += $row['total_orders'];
                }
                echo number_format($orders);
                ?></h3>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card">
            <h6>Average Order Value</h6>
            <h3>$<?php
                    echo $orders > 0 ? number_format($total / $orders, 2) : '0.00';
                    ?></h3>
        </div>
    </div>
</div>