<?php
// Include database configuration
require_once '../../config/config.php';

// Process form submission for adding a role
if (isset($_POST['add_role'])) {
    $role_name = $_POST['role_name'];
    $role_description = $_POST['role_description'];
    $permissions = $_POST['permissions'];

    $stmt = $conn->prepare("INSERT INTO roles (name, description, permissions) VALUES (?, ?, ?)");
    if ($stmt->execute([$role_name, $role_description, $permissions])) {
        $success_message = "New role added successfully";
    } else {
        $error_message = "Error: Could not add role.";
    }
}

// Get all roles for the table
$sql = "SELECT * FROM roles";
$roles_result = $conn->query($sql);
$roles = $roles_result ? $roles_result->fetchAll(PDO::FETCH_ASSOC) : [];
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Role Management</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
    <style>
        .form-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Title styling with orange background */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            background-color: #f15b31;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: inline-block;
            min-width: 200px;
            text-align: center;
        }

        /* Button styling with orange theme */
        .btn-primary {
            background-color: #f15b31;
            border-color: #f15b31;
        }

        .btn-primary:hover {
            background-color: #d14118;
            border-color: #d14118;
        }

        /* Card header styling */
        .card-header h4 {
            background-color: transparent;
            color: #333;
            padding: 0;
            margin: 0;
            display: block;
            text-align: left;
        }

        /* Navbar styling with orange theme */
        .navbar {
            background-color: #f15b31 !important;
        }

        .navbar-brand {
            color: white !important;
            font-weight: bold;
        }

        .navbar-nav .nav-link {
            color: white !important;
        }

        .navbar-nav .nav-link:hover {
            color: #ffccbb !important;
        }

        .navbar-nav .nav-link.active {
            color: #ffccbb !important;
            font-weight: bold;
        }

        /* Back to Dashboard button */
        .back-to-dashboard {
            background-color: #f15b31;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            transition: background-color 0.3s;
        }

        .back-to-dashboard:hover {
            background-color: #d14118;
            color: white;
            text-decoration: none;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Human Resources Management System</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../user/dashboard_user.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../user/user_management.php">User Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/add_driver.php">Driver Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/departments.php">Departments Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/roles_management.php">Roles Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/add_user.php">Add User Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../finance/payroll.php">Payroll Management</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../auth/logout.php">Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <div class="container mt-4">
        <h1 class="text-center mb-4">Role Management</h1>

        <?php if (isset($success_message)): ?>
            <div class="alert alert-success">
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <div class="form-container">
            <div class="card">
                <div class="card-header">
                    <h4>Add New Role</h4>
                </div>
                <div class="card-body">
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                        <div class="mb-3">
                            <label for="role_name" class="form-label">Role Name</label>
                            <input type="text" class="form-control" id="role_name" name="role_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="role_description" class="form-label">Description</label>
                            <textarea class="form-control" id="role_description" name="role_description" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="permissions" class="form-label">Permissions</label>
                            <textarea class="form-control" id="permissions" name="permissions" rows="3" required></textarea>
                            <small class="form-text text-muted">Enter permissions as a comma-separated list</small>
                        </div>
                        <button type="submit" name="add_role" class="btn btn-primary">Add Role</button>
                    </form>
                </div>
            </div>

            <!-- Roles List Table -->
            <div class="card mt-4">
                <div class="card-header">
                    <h4>Roles List</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Permissions</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($roles as $role): ?>
                                    <tr>
                                        <td><?php echo $role['id']; ?></td>
                                        <td><?php echo htmlspecialchars($role['name']); ?></td>
                                        <td><?php echo htmlspecialchars($role['description']); ?></td>
                                        <td><?php echo htmlspecialchars($role['permissions']); ?></td>
                                        <td>
                                            <a href="edit_role.php?id=<?php echo $role['id']; ?>" class="btn btn-sm btn-primary">Edit</a>
                                            <a href="delete_role.php?id=<?php echo $role['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this role?')">Delete</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back to Dashboard Button -->
        <div class="text-center mt-4 mb-4">
            <a href="../admin/admin_dashboard.php" class="back-to-dashboard">Back to Dashboard</a>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
</body>

</html>