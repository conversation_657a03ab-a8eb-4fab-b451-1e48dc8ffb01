<?php
// No need to include config.php here as it's already included in the parent file
?>
<div id="overallTotals" class="mb-3"></div>
<div class="row">
    <div class="col-md-6">
        <div class="dashboard-card p-3">
            <h5>Inventory by Brand (Realtime)</h5>
            <div class="chart-container" style="height:350px;">
                <canvas id="inventoryChart"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="dashboard-card p-3">
            <h5>Brand Inventory Details</h5>
            <div class="table-responsive">
                <table class="table table-bordered" id="brandInventoryTable">
                    <thead>
                        <tr>
                            <th>Brand</th>
                            <th>Total Products</th>
                            <th>Total Stock</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Filled by JS -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- Low Stock Items Table -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="dashboard-card p-3">
            <h5>Low Stock Items (Stock &lt; 10)</h5>
            <div class="table-responsive">
                <table class="table table-bordered" id="lowStockTable">
                    <thead>
                        <tr>
                            <th>Product Name</th>
                            <th>Brand</th>
                            <th>Stock</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Filled by JS -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
<script>
    const inventoryCtx = document.getElementById('inventoryChart').getContext('2d');
    let inventoryChart;

    function fetchAndUpdateInventory() {
        fetch('../includes/get_inventory_data.php')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                console.log('Inventory data received:', data);
                const brands = data.map(item => item.brand_name);
                const stocks = data.map(item => item.total_stock);
                const products = data.map(item => item.total_products);

                // Calculate overall totals
                const totalAllStocks = stocks.reduce((a, b) => a + b, 0);
                const totalAllProducts = products.reduce((a, b) => a + b, 0);
                document.getElementById('overallTotals').innerHTML =
                    `<b>All Brands:</b> Total Products: <span class='text-primary'>${totalAllProducts}</span> &nbsp; | &nbsp; Total Stocks: <span class='text-success'>${totalAllStocks}</span>`;

                // Update Chart
                if (!inventoryChart) {
                    inventoryChart = new Chart(inventoryCtx, {
                        type: 'pie',
                        data: {
                            labels: brands,
                            datasets: [{
                                data: stocks,
                                backgroundColor: [
                                    '#4b6cb7', '#182848', '#2c3e50', '#3498db', '#2980b9', '#e67e22', '#16a085', '#8e44ad', '#c0392b'
                                ]
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            const idx = context.dataIndex;
                                            return brands[idx] + ': ' + stocks[idx] + ' stocks, ' + products[idx] + ' products';
                                        }
                                    }
                                }
                            }
                        }
                    });
                } else {
                    inventoryChart.data.labels = brands;
                    inventoryChart.data.datasets[0].data = stocks;
                    inventoryChart.update();
                }

                // Update Table
                const tbody = document.querySelector('#brandInventoryTable tbody');
                tbody.innerHTML = '';
                data.forEach(item => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `<td>${item.brand_name}</td><td>${item.total_products}</td><td>${item.total_stock}</td>`;
                    tbody.appendChild(tr);
                });
            });

        // Fetch low stock items
        fetch('../includes/get_low_stock.php')
            .then(response => response.json())
            .then(data => {
                const tbody = document.querySelector('#lowStockTable tbody');
                tbody.innerHTML = '';
                if (data.length > 0) {
                    data.forEach(item => {
                        const tr = document.createElement('tr');
                        tr.innerHTML = `<td>${item.name}</td><td>${item.brand_name}</td><td>${item.stock}</td>`;
                        tbody.appendChild(tr);
                    });
                } else {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `<td colspan='3' class='text-center'>No low stock items</td>`;
                    tbody.appendChild(tr);
                }
            });
    }

    fetchAndUpdateInventory();
    setInterval(fetchAndUpdateInventory, 10000);
</script>