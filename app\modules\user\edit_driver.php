<?php
session_start();
require_once '../../config/config.php';
require_once '../../includes/export_functions.php';

// Check if user is logged in and has admin role
if (
    !isset($_SESSION['user_id']) ||
    (isset($_SESSION['role']) && $_SESSION['role'] !== 'admin') &&
    (isset($_SESSION['user_role']) && $_SESSION['user_role'] !== 'admin')
) {
    header("Location: ../../auth/login.php");
    exit();
}

$success_message = '';
$error_message = '';

// Check if driver ID is provided
if (!isset($_GET['id'])) {
    header("Location: add_driver.php");
    exit();
}

$driver_id = $_GET['id'];

// Get driver profile
$driver_sql = "SELECT d.*, CONCAT(u.first_name, ' ', u.last_name) as driver_name, u.email
              FROM drivers d
              JOIN users u ON d.user_id = u.id
              WHERE d.user_id = ?";
$stmt = $conn->prepare($driver_sql);
$stmt->execute([$driver_id]);
$driver = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$driver) {
    header("Location: add_driver.php");
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $vehicle_type = $_POST['vehicle_type'];
    $license_number = $_POST['license_number'];
    $phone = $_POST['phone'];
    $availability = $_POST['availability'];

    // Update driver profile
    $sql = "UPDATE drivers SET
            vehicle_type = ?,
            license_number = ?,
            phone = ?,
            availability = ?
            WHERE user_id = ?";
    $stmt = $conn->prepare($sql);
    $result = $stmt->execute([$vehicle_type, $license_number, $phone, $availability, $driver_id]);

    if ($result) {
        $success_message = "Driver profile updated successfully!";

        // Refresh driver data
        $stmt = $conn->prepare($driver_sql);
        $stmt->execute([$driver_id]);
        $driver = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        $error_message = "Error updating driver profile.";
    }
}

// Handle driver deletion
if (isset($_POST['delete_driver'])) {
    // Delete driver profile
    $delete_sql = "DELETE FROM drivers WHERE user_id = ?";
    $stmt = $conn->prepare($delete_sql);
    $result = $stmt->execute([$driver_id]);

    if ($result) {
        // Update user role back to user
        $update_role_sql = "UPDATE users SET role = 'user' WHERE id = ?";
        $stmt = $conn->prepare($update_role_sql);
        $stmt->execute([$driver_id]);

        header("Location: add_driver.php?deleted=1");
        exit();
    } else {
        $error_message = "Error deleting driver profile.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Driver Profile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Title styling with orange background */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            background-color: #f15b31;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: inline-block;
            min-width: 200px;
            text-align: center;
        }

        /* Button styling with orange theme */
        .btn-primary {
            background-color: #f15b31;
            border-color: #f15b31;
        }

        .btn-primary:hover {
            background-color: #d14118;
            border-color: #d14118;
        }

        /* Card header styling */
        .card-header h5 {
            background-color: transparent;
            color: #333;
            padding: 0;
            margin: 0;
            display: block;
            text-align: left;
        }

        /* Back to Dashboard button */
        .back-to-dashboard {
            background-color: #f15b31;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            transition: background-color 0.3s;
        }

        .back-to-dashboard:hover {
            background-color: #d14118;
            color: white;
            text-decoration: none;
        }

        /* Navbar styling with orange theme */
        .navbar {
            background-color: #f15b31 !important;
        }

        .navbar-brand {
            color: white !important;
            font-weight: bold;
        }

        .navbar-nav .nav-link {
            color: white !important;
        }

        .navbar-nav .nav-link:hover {
            color: #ffccbb !important;
        }

        .navbar-nav .nav-link.active {
            color: #ffccbb !important;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Human Resources Management System</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../user/dashboard_user.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../user/user_management.php">User Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/add_driver.php">Driver Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/departments.php">Departments Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/roles_management.php">Roles Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/add_user.php">Add User Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../finance/payroll.php">Payroll Management</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../auth/logout.php">Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Edit Driver Profile</h5>
                        <a href="add_driver.php" class="btn btn-sm btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Drivers
                        </a>
                    </div>
                    <div class="card-body">
                        <?php if ($success_message): ?>
                            <div class="alert alert-success">
                                <?php echo $success_message; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($error_message): ?>
                            <div class="alert alert-danger">
                                <?php echo $error_message; ?>
                            </div>
                        <?php endif; ?>

                        <div class="alert alert-info">
                            <strong>Driver:</strong> <?php echo htmlspecialchars($driver['driver_name']); ?><br>
                            <strong>Email:</strong> <?php echo htmlspecialchars($driver['email']); ?>
                        </div>

                        <form method="POST">
                            <div class="mb-3">
                                <label>Vehicle Type</label>
                                <select name="vehicle_type" class="form-control" required>
                                    <option value="motorcycle" <?php echo $driver['vehicle_type'] === 'motorcycle' ? 'selected' : ''; ?>>Motorcycle</option>
                                    <option value="car" <?php echo $driver['vehicle_type'] === 'car' ? 'selected' : ''; ?>>Car</option>
                                    <option value="van" <?php echo $driver['vehicle_type'] === 'van' ? 'selected' : ''; ?>>Van</option>
                                    <option value="truck" <?php echo $driver['vehicle_type'] === 'truck' ? 'selected' : ''; ?>>Truck</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label>License Number</label>
                                <input type="text" name="license_number" class="form-control" value="<?php echo htmlspecialchars($driver['license_number']); ?>" required>
                            </div>

                            <div class="mb-3">
                                <label>Phone</label>
                                <input type="tel" name="phone" class="form-control" value="<?php echo htmlspecialchars($driver['phone']); ?>" required>
                            </div>

                            <div class="mb-3">
                                <label>Availability</label>
                                <select name="availability" class="form-control" required>
                                    <option value="available" <?php echo $driver['availability'] === 'available' ? 'selected' : ''; ?>>Available</option>
                                    <option value="busy" <?php echo $driver['availability'] === 'busy' ? 'selected' : ''; ?>>Busy</option>
                                    <option value="offline" <?php echo $driver['availability'] === 'offline' ? 'selected' : ''; ?>>Offline</option>
                                </select>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Driver Profile
                                </button>

                                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                    <i class="fas fa-trash"></i> Remove Driver
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Confirm Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to remove <strong><?php echo htmlspecialchars($driver['driver_name']); ?></strong> as a driver?</p>
                    <p>This will delete their driver profile and change their role back to a regular user.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST">
                        <input type="hidden" name="delete_driver" value="1">
                        <button type="submit" class="btn btn-danger">Delete Driver</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Dashboard Button -->
    <div class="text-center mt-4 mb-4">
        <a href="../admin/admin_dashboard.php" class="back-to-dashboard">Back to Dashboard</a>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
</body>

</html>