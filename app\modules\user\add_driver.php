<?php
require_once '../../config/config.php';


$success_message = '';
$error_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_driver'])) {
    // Get form data
    $user_id = $_POST['user_id'];
    $vehicle_type = $_POST['vehicle_type'];
    $license_number = $_POST['license_number'];
    $phone = $_POST['phone'];
    $availability = $_POST['availability'];

    // Check if driver profile already exists
    $check_sql = "SELECT * FROM drivers WHERE user_id = ?";
    $stmt = $conn->prepare($check_sql);
    $stmt->execute([$user_id]);
    $existing_driver = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($existing_driver) {
        // Update existing driver profile
        $sql = "UPDATE drivers SET
                vehicle_type = ?,
                license_number = ?,
                phone = ?,
                availability = ?
                WHERE user_id = ?";
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute([$vehicle_type, $license_number, $phone, $availability, $user_id]);

        if ($result) {
            $success_message = "Driver profile updated successfully!";
        } else {
            $error_message = "Error updating driver profile.";
        }
    } else {
        // Create new driver profile
        $sql = "INSERT INTO drivers (user_id, vehicle_type, license_number, phone, availability)
                VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute([$user_id, $vehicle_type, $license_number, $phone, $availability]);

        if ($result) {
            $success_message = "Driver profile created successfully!";
        } else {
            $error_message = "Error creating driver profile.";
        }
    }

    // Update user role to logistics if needed
    // First check which column exists in the users table
    try {
        $check_column_sql = "SHOW COLUMNS FROM users LIKE 'role'";
        $stmt = $conn->prepare($check_column_sql);
        $stmt->execute();
        $role_column_exists = $stmt->rowCount() > 0;

        $check_column_sql = "SHOW COLUMNS FROM users LIKE 'user_role'";
        $stmt = $conn->prepare($check_column_sql);
        $stmt->execute();
        $user_role_column_exists = $stmt->rowCount() > 0;

        // Update the appropriate column
        if ($role_column_exists) {
            $update_role_sql = "UPDATE users SET role = 'logistics' WHERE id = ?";
            $stmt = $conn->prepare($update_role_sql);
            $stmt->execute([$user_id]);
        } elseif ($user_role_column_exists) {
            $update_role_sql = "UPDATE users SET user_role = 'logistics' WHERE id = ?";
            $stmt = $conn->prepare($update_role_sql);
            $stmt->execute([$user_id]);
        } else {
            // If neither column exists, log an error
            error_log("Neither 'role' nor 'user_role' column found in users table");
        }
    } catch (PDOException $e) {
        error_log("Error updating user role: " . $e->getMessage());
    }
}

// Handle driver deletion if coming from edit page
if (isset($_GET['deleted']) && $_GET['deleted'] == '1') {
    $success_message = "Driver profile deleted successfully!";
}

// Get all users - select all columns to avoid column name issues
$users_sql = "SELECT * FROM users ORDER BY first_name, last_name";
$users = $conn->query($users_sql)->fetchAll(PDO::FETCH_ASSOC);

// Process users to create consistent data structure
$processed_users = [];
foreach ($users as $user) {
    $role_value = '';
    // Check different possible role column names
    if (isset($user['role'])) {
        $role_value = $user['role'];
    } elseif (isset($user['user_role'])) {
        $role_value = $user['user_role'];
    }

    $processed_users[] = [
        'id' => $user['id'],
        'full_name' => $user['first_name'] . ' ' . $user['last_name'],
        'email' => $user['email'],
        'role' => $role_value
    ];
}
$users = $processed_users;
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Driver Management - Finance Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .driver-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .table-responsive {
            margin-top: 20px;
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 0.9em;
        }

        .status-available {
            background-color: #28a745;
            color: white;
        }

        .status-busy {
            background-color: #ffc107;
            color: black;
        }

        .status-offline {
            background-color: #6c757d;
            color: white;
        }

        /* Title styling with orange background */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            background-color: #f15b31;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: inline-block;
            min-width: 200px;
            text-align: center;
        }

        /* Button styling with orange theme */
        .btn-primary {
            background-color: #f15b31;
            border-color: #f15b31;
        }

        .btn-primary:hover {
            background-color: #d14118;
            border-color: #d14118;
        }

        /* Card header styling */
        .card-header h4 {
            background-color: transparent;
            color: #333;
            padding: 0;
            margin: 0;
            display: block;
            text-align: left;
        }

        /* Back to Dashboard button */
        .back-to-dashboard {
            background-color: #f15b31;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 20px;
            transition: background-color 0.3s;
        }

        .back-to-dashboard:hover {
            background-color: #d14118;
            color: white;
            text-decoration: none;
        }

        /* Navbar styling with orange theme */
        .navbar {
            background-color: #f15b31 !important;
        }

        .navbar-brand {
            color: white !important;
            font-weight: bold;
        }

        .navbar-nav .nav-link {
            color: white !important;
        }

        .navbar-nav .nav-link:hover {
            color: #ffccbb !important;
        }

        .navbar-nav .nav-link.active {
            color: #ffccbb !important;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Human Resources Management System</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../user/dashboard_user.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../user/user_management.php">User Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/add_driver.php">Driver Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/departments.php">Departments Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/roles_management.php">Roles Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../user/add_user.php">Add User Management</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../finance/payroll.php">Payroll Management</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../auth/logout.php">Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <div class="form-container">
            <h2 class="text-center mb-4">Driver Management</h2>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h4>Add New Driver</h4>
                        </div>
                        <div class="card-body driver-form">
                            <form method="POST" action="">
                                <input type="hidden" name="add_driver" value="1">
                                <div class="mb-3">
                                    <label for="user_id" class="form-label">Select User</label>
                                    <select class="form-control" id="user_id" name="user_id" required>
                                        <option value="">-- Select User --</option>
                                        <?php foreach ($users as $user): ?>
                                            <option value="<?php echo $user['id']; ?>">
                                                <?php echo htmlspecialchars($user['full_name']); ?>
                                                (<?php echo htmlspecialchars($user['email']); ?>)
                                                <?php if ($user['role'] === 'logistics'): ?>
                                                    - Current Driver
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <small class="form-text text-muted">
                                        Selecting a user will automatically assign them the "logistics" role.
                                    </small>
                                </div>

                                <div class="mb-3">
                                    <label for="vehicle_type" class="form-label">Vehicle Type</label>
                                    <select class="form-control" id="vehicle_type" name="vehicle_type" required>
                                        <option value="motorcycle">Motorcycle</option>
                                        <option value="car">Car</option>
                                        <option value="van">Van</option>
                                        <option value="truck">Truck</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="license_number" class="form-label">License Number</label>
                                    <input type="text" class="form-control" id="license_number" name="license_number" required>
                                </div>

                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required>
                                </div>

                                <div class="mb-3">
                                    <label for="availability" class="form-label">Availability</label>
                                    <select class="form-control" id="availability" name="availability" required>
                                        <option value="available">Available</option>
                                        <option value="busy">Busy</option>
                                        <option value="offline">Offline</option>
                                    </select>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Driver Profile
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h4>Driver Profiles</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Driver Name</th>
                                            <th>Vehicle Type</th>
                                            <th>License Number</th>
                                            <th>Phone</th>
                                            <th>Availability</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $drivers_sql = "SELECT d.*, CONCAT(u.first_name, ' ', u.last_name) as driver_name, u.email
                                                      FROM drivers d
                                                      JOIN users u ON d.user_id = u.id
                                                      ORDER BY driver_name";
                                        $drivers = $conn->query($drivers_sql)->fetchAll(PDO::FETCH_ASSOC);

                                        if (count($drivers) > 0):
                                            foreach ($drivers as $driver):
                                        ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($driver['driver_name']); ?></td>
                                                    <td><?php echo ucfirst(htmlspecialchars($driver['vehicle_type'])); ?></td>
                                                    <td><?php echo htmlspecialchars($driver['license_number']); ?></td>
                                                    <td><?php echo htmlspecialchars($driver['phone']); ?></td>
                                                    <td>
                                                        <span class="status-badge status-<?php echo $driver['availability']; ?>">
                                                            <?php echo ucfirst($driver['availability']); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <a href="edit_driver.php?id=<?php echo $driver['user_id']; ?>" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php
                                            endforeach;
                                        else:
                                            ?>
                                            <tr>
                                                <td colspan="6" class="text-center">No driver profiles found.</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Dashboard Button -->
    <div class="text-center mt-4 mb-4">
        <a href="../admin/admin_dashboard.php" class="back-to-dashboard">Back to Dashboard</a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>