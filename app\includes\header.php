<?php
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . BASE_URL . '/app/modules/auth/login.php');
    exit();
}
?>
<style>
    /* Navbar styling with orange theme */
    .navbar {
        background-color: #f15b31 !important;
    }

    .navbar-brand {
        color: white !important;
        font-weight: bold;
    }

    .navbar-nav .nav-link {
        color: white !important;
    }

    .navbar-nav .nav-link:hover {
        color: #ffccbb !important;
    }

    .navbar-nav .nav-link.active {
        color: #ffccbb !important;
        font-weight: bold;
    }

    .dropdown-menu {
        background-color: white;
    }

    .dropdown-item {
        color: #333 !important;
    }

    .dropdown-item:hover {
        background-color: #f15b31;
        color: white !important;
    }
</style>
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="<?php echo BASE_URL; ?>/app/modules/admin/admin_dashboard.php">Finance Management System</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <?php if ($_SESSION['user_role'] === 'admin'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/app/modules/admin/admin_dashboard.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/app/modules/user/user_management.php">User Management</a>
                    </li>
                <?php endif; ?>

                <?php if (in_array($_SESSION['user_role'], ['admin', 'finance'])): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/app/modules/finance/financial_reports.php">Finance</a>
                    </li>
                <?php endif; ?>

                <?php if (in_array($_SESSION['user_role'], ['admin', 'inventory'])): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/app/modules/inventory/inventory.php">Inventory</a>
                    </li>
                <?php endif; ?>

                <?php if (in_array($_SESSION['user_role'], ['admin', 'logistics'])): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/app/modules/logistics/logistics_management.php">Logistics</a>
                    </li>
                <?php endif; ?>

                <?php if (in_array($_SESSION['user_role'], ['admin', 'warehouse'])): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/app/modules/warehouse/warehouse_management.php">Warehouse</a>
                    </li>
                <?php endif; ?>

                <?php if (in_array($_SESSION['user_role'], ['admin', 'payroll'])): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/app/modules/payroll/payroll.php">Payroll</a>
                    </li>
                <?php endif; ?>

                <?php if (in_array($_SESSION['user_role'], ['admin', 'finance', 'payroll'])): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/app/modules/reports/reports.php">Reports</a>
                    </li>
                <?php endif; ?>
            </ul>
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <?php echo htmlspecialchars($_SESSION['username']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/app/modules/user/profile.php">Profile</a></li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/app/modules/auth/logout.php">Logout</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>